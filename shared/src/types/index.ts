export type ApiResponse = {
  message: string;
  success: true;
}

export type DashboardStats = {
  totalPengunjungTerdaftar: number;
  totalPengunjungDisetujui: number;
  totalPengunjungDitolak: number;
  totalPengunjungKadaluwarsa: number;
}

export type ChartDataPoint = {
  day: string;
  value: number;
}

export type VisitorData = {
  id: string;
  name: string;
  company: string;
  type: 'PAM' | 'SMA' | 'IBM';
  status: 'approved' | 'pending' | 'rejected';
  date: string;
  location?: {
    lat: number;
    lng: number;
  };
}

// Auth Types
export type UserRole = 'user' | 'admin' | 'superadmin';

export type User = {
  id: string;
  email: string;
  name?: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export type AuthSession = {
  user: User;
  session: {
    id: string;
    expiresAt: Date;
  };
}

export type LoginRequest = {
  email: string;
  password: string;
}

export type SignupRequest = {
  email: string;
  password: string;
  name?: string;
}
