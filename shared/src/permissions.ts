import { createAccessControl } from "better-auth/plugins/access"

export const statement = {
  user: ["set-role", "ban", "impersonate", "delete"],
  project: ["create", "read", "update", "delete"],
  visitor: ["create", "read", "update", "delete", "approve", "reject"],
  content: ["create", "read", "update", "delete"],
  system: ["backup", "restore", "logs", "settings"],
} as const

export const ac = createAccessControl(statement)

export const roles = {
  superadmin: ac.newRole({
    user: ["set-role", "ban", "impersonate", "delete"],
    project: ["create", "read", "update", "delete"],
    visitor: ["create", "read", "update", "delete", "approve", "reject"],
    content: ["create", "read", "update", "delete"],
    system: ["backup", "restore", "logs", "settings"],
  }),
  admin: ac.newRole({
    user: ["set-role"],
    project: ["create", "read", "update"],
    visitor: ["create", "read", "update", "approve", "reject"],
    content: ["create", "read", "update"],
    system: ["logs"],
  }),
  user: ac.newRole({
    project: ["read"],
    visitor: ["read"],
    content: ["read"],
  }),
}
