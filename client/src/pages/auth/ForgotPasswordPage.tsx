import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { PAMLogo } from '../../components/ui/PAMLogo';

export function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setMessage('');

    try {
      // TODO: Implement forgot password functionality
      // For now, just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage('Link reset password telah dikirim ke email Anda.');
    } catch (err) {
      setError('<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left side - Logo (hidden on mobile) */}
      <div className="hidden lg:flex lg:w-1/2 items-center justify-center bg-white">
        <PAMLogo className="scale-150" />
      </div>

      {/* Right side - Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Mobile Logo */}
          <div className="lg:hidden mb-8 flex justify-center">
            <PAMLogo />
          </div>

          {/* Form Header */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">SELAMAT DATANG</h1>
            <p className="text-gray-600">Lupa Password</p>
            <p className="text-sm text-gray-500 mt-2">
              Masukkan email untuk mendapatkan link reset password.
            </p>
          </div>

          {/* Success Message */}
          {message && (
            <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md mb-6">
              {message}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-6">
              {error}
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full h-12 px-4 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Email"
              />
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full h-12 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-md transition-colors"
            >
              {isLoading ? 'Mengirim...' : 'Kirim Link Verifikasi'}
            </Button>

            {/* Back to Login Button */}
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/login')}
              className="w-full h-12 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors"
            >
              Kembali ke Login
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}
