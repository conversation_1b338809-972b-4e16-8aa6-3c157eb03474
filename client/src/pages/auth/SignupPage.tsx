import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SignupForm } from '../../components/auth/SignupForm';
import { useAuth } from '../../contexts/AuthContext';

export function SignupPage() {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect based on user role
      if (user.role === 'superadmin') {
        navigate('/superadmin/dashboard', { replace: true });
      } else {
        navigate('/dashboard', { replace: true });
      }
    }
  }, [isAuthenticated, user, navigate]);

  return <SignupForm />;
}
