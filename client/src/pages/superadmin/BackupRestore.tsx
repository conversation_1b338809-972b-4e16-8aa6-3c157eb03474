import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Badge } from '../../components/ui/badge';
import { Download, RotateCcw, Trash2 } from 'lucide-react';

interface BackupLog {
  id: string;
  date: string;
  type: 'Auto-Backup' | 'Manual';
  status: 'Berhasil' | 'Gagal';
  size: string;
}

export function BackupRestore() {
  const [backupSchedule, setBackupSchedule] = useState('harian');
  const [backupLogs] = useState<BackupLog[]>([
    {
      id: '1',
      date: '2023-10-01',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.5 GB'
    },
    {
      id: '2',
      date: '2023-09-24',
      type: 'Manual',
      status: 'Gagal',
      size: '1.2 GB'
    },
    {
      id: '3',
      date: '2023-09-15',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.3 GB'
    }
  ]);

  const getStatusBadge = (status: string) => {
    return status === 'Berhasil' 
      ? <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Berhasil</Badge>
      : <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Gagal</Badge>;
  };

  const BackupContent = () => (
    <div className="space-y-6">
      {/* Auto Backup Schedule & Export/Import */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Jadwal Auto-Backup */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Jadwal Auto-Backup</h3>
            <div className="space-y-4">
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="backup-schedule"
                    value="harian"
                    checked={backupSchedule === 'harian'}
                    onChange={(e) => setBackupSchedule(e.target.value)}
                    className="text-orange-500"
                  />
                  <span className="text-sm text-gray-700">Harian</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="backup-schedule"
                    value="mingguan"
                    checked={backupSchedule === 'mingguan'}
                    onChange={(e) => setBackupSchedule(e.target.value)}
                    className="text-orange-500"
                  />
                  <span className="text-sm text-gray-700">Mingguan</span>
                </label>
              </div>
              <div className="flex gap-3">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2">
                  Backup Sekarang
                </Button>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2">
                  Atur Jadwal
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export/Import Data */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Export/Import Data</h3>
            <div className="space-y-3">
              <Button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2">
                Export as CSV
              </Button>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2">
                Export as JSON
              </Button>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2">
                Import CSV/JSON
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Log Riwayat Backup */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Log Riwayat Backup</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left p-3 font-medium text-gray-900">Tanggal</th>
                  <th className="text-left p-3 font-medium text-gray-900">Tipe</th>
                  <th className="text-left p-3 font-medium text-gray-900">Status</th>
                  <th className="text-left p-3 font-medium text-gray-900">Ukuran</th>
                  <th className="text-left p-3 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {backupLogs.map((log) => (
                  <tr key={log.id} className="border-b hover:bg-gray-50">
                    <td className="p-3 text-gray-900">{log.date}</td>
                    <td className="p-3 text-gray-600">{log.type}</td>
                    <td className="p-3">{getStatusBadge(log.status)}</td>
                    <td className="p-3 text-gray-600">{log.size}</td>
                    <td className="p-3">
                      <div className="flex space-x-2">
                        <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 text-xs">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 text-xs">
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Restore
                        </Button>
                        <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 text-xs">
                          <Trash2 className="h-3 w-3 mr-1" />
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Indikator Penyimpanan */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Indikator Penyimpanan</h3>
          <div className="flex items-center space-x-4">
            <div className="w-32 h-32 relative">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="3"
                  strokeDasharray="65, 100"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-900">65%</div>
                  <div className="text-xs text-gray-600">Used</div>
                </div>
              </div>
            </div>
            <div className="flex-1">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Sisa Penyimpanan: 35 GB</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Digunakan: 65 GB</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900">
          Backup & Restore
        </h1>
      </div>

      <Tabs defaultValue="PAM" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
          <TabsTrigger
            value="PAM"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            PAM
          </TabsTrigger>
          <TabsTrigger
            value="SMA"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            SMA
          </TabsTrigger>
          <TabsTrigger
            value="IBM"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            IBM
          </TabsTrigger>
        </TabsList>

        <TabsContent value="PAM" className="space-y-6 mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>
      </Tabs>
    </div>
  );
}
