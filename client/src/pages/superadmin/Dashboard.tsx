import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { StatsCards } from '../../components/dashboard/StatsCards';
import { VisitorChart } from '../../components/dashboard/VisitorChart';
import { HeatmapCard } from '../../components/dashboard/HeatmapCard';
import { RecentVisitors } from '../../components/dashboard/RecentVisitors';
import { hcWithType } from 'server/dist/client';
import type { DashboardStats, ChartDataPoint, VisitorData } from 'shared/dist';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || "http://localhost:3000";
const client = hcWithType(SERVER_URL);

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Mock data for stats
        setStats({
          totalPengunjungTerdaftar: 17,
          totalPengunjungDisetujui: 10,
          totalPengunjungDitolak: 2,
          totalPengunjungKadaluwarsa: 5,
        });

        // Mock data for chart
        const mockChartData: ChartDataPoint[] = [
          { day: 'Senin', value: 42 },
          { day: 'Selasa', value: 38 },
          { day: 'Rabu', value: 45 },
          { day: 'Kamis', value: 52 },
          { day: 'Jumat', value: 48 },
          { day: 'Sabtu', value: 35 },
          { day: 'Minggu', value: 28 }
        ];
        setChartData(mockChartData);

        // Mock data for visitors
        const mockVisitors: VisitorData[] = [
          {
            id: '1',
            name: 'Rina Pratama',
            company: 'PT. PAM MINERAL TBK',
            type: 'PAM',
            status: 'approved',
            date: '2024-01-15',
            location: { lat: -6.2088, lng: 106.8456 }
          },
          {
            id: '2',
            name: 'Budi Santoso',
            company: 'PT. PAM MINERAL TBK',
            type: 'PAM',
            status: 'pending',
            date: '2024-01-14',
            location: { lat: -6.2000, lng: 106.8300 }
          },
          {
            id: '3',
            name: 'Sari Wijaya',
            company: 'PT. SMA INDONESIA',
            type: 'SMA',
            status: 'approved',
            date: '2024-01-13',
            location: { lat: -6.1900, lng: 106.8200 }
          },
          {
            id: '4',
            name: 'Ahmad Fauzi',
            company: 'IBM Indonesia',
            type: 'IBM',
            status: 'rejected',
            date: '2024-01-12',
            location: { lat: -6.1800, lng: 106.8100 }
          }
        ];
        setVisitors(mockVisitors);

        // Try to fetch real data if available
        try {
          const statsRes = await client.api.protected.dashboard.stats.$get();
          if (statsRes.ok) {
            const statsData = await statsRes.json();
            setStats(statsData);
          }

          const chartRes = await client.api.protected.dashboard.chart.$get();
          if (chartRes.ok) {
            const chartData = await chartRes.json();
            setChartData(chartData);
          }

          const visitorsRes = await client.api.protected.dashboard.visitors.$get();
          if (visitorsRes.ok) {
            const visitorsData = await visitorsRes.json();
            setVisitors(visitorsData);
          }
        } catch (error) {
          console.error('Error fetching dashboard data:', error);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Dashboard</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger
              value="PAM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger
              value="SMA"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger
              value="IBM"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="space-y-4 lg:space-y-6 mt-4 lg:mt-6">
          {/* Stats Cards */}
          <StatsCards stats={stats} />

          {/* Chart and Heatmap */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>

          {/* Recent Visitors */}
          <RecentVisitors visitors={visitors} />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-4 lg:space-y-6 mt-4 lg:mt-6">
          <StatsCards stats={stats} />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>
          <RecentVisitors visitors={visitors.filter(v => v.type === 'SMA')} />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-4 lg:space-y-6 mt-4 lg:mt-6">
          <StatsCards stats={stats} />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>
          <RecentVisitors visitors={visitors.filter(v => v.type === 'IBM')} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
