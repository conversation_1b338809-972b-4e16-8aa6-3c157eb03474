import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Upload } from 'lucide-react';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';

interface Question {
  id: string;
  question: string;
  options: { id: string; text: string; isCorrect: boolean }[];
}

export function ManajemenKonten() {
  const [isEditingEmployee, setIsEditingEmployee] = useState(false);
  const [isEditingGeneral, setIsEditingGeneral] = useState(false);

  const [employeeQuestions, setEmployeeQuestions] = useState<Question[]>([
    {
      id: '1',
      question: 'Apa tujuan utama dari safety induction?',
      options: [
        { id: '1a', text: 'Hijau: petunjuk/informasi', isCorrect: false },
        { id: '1b', text: 'Kuning: peringatan', isCorrect: false },
        { id: '1c', text: 'Hitam: Beracun', isCorrect: true },
        { id: '1d', text: 'Merah: larangan', isCorrect: false }
      ]
    },
    {
      id: '2',
      question: 'Apakah yang harus dilakukan Visitor jika melihat percikan api/kebakaran?',
      options: [
        { id: '2a', text: 'Melaporkan ke pendamping, dalam situasi darurat boleh melakukan pemadaman dengan pemahaman APAR', isCorrect: true },
        { id: '2b', text: 'Langsung padamkan api menggunakan air terdekat', isCorrect: false },
        { id: '2c', text: 'Mencari karung goni untuk memadamkan api', isCorrect: false },
        { id: '2d', text: 'Berteriak sekencang-kencangnya', isCorrect: false }
      ]
    }
  ]);

  const [generalQuestions, setGeneralQuestions] = useState<Question[]>([
    {
      id: '1',
      question: 'Apa tujuan utama dari safety induction?',
      options: [
        { id: '1a', text: 'Hijau: petunjuk/informasi', isCorrect: false },
        { id: '1b', text: 'Kuning: peringatan', isCorrect: false },
        { id: '1c', text: 'Hitam: Beracun', isCorrect: true },
        { id: '1d', text: 'Merah: larangan', isCorrect: false }
      ]
    },
    {
      id: '2',
      question: 'Apakah yang harus dilakukan Visitor jika melihat percikan api/kebakaran?',
      options: [
        { id: '2a', text: 'Melaporkan ke pendamping, dalam situasi darurat boleh melakukan pemadaman dengan pemahaman APAR', isCorrect: true },
        { id: '2b', text: 'Langsung padamkan api menggunakan air terdekat', isCorrect: false },
        { id: '2c', text: 'Mencari karung goni untuk memadamkan api', isCorrect: false },
        { id: '2d', text: 'Berteriak sekencang-kencangnya', isCorrect: false }
      ]
    }
  ]);

  const VideoUploadSection = ({ title }: { title: string }) => (
    <Card className="bg-white border border-gray-200">
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50">
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Upload Video</h4>
            <p className="text-sm text-gray-600">Max 500MB</p>
            <Button
              variant="outline"
              className="mt-4 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const QuizSection = ({
    questions,
    isEditing,
    setIsEditing,
    setQuestions
  }: {
    questions: Question[],
    isEditing: boolean,
    setIsEditing: (editing: boolean) => void,
    setQuestions: (questions: Question[]) => void
  }) => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Left Column - Quiz Question Editor */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Quiz Question Editor</h3>
            <Button
              onClick={() => setIsEditing(!isEditing)}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2"
            >
              {isEditing ? 'Simpan Edit' : 'Edit'}
            </Button>
          </div>

          <p className="text-sm text-gray-600 mb-6">
            Gunakan penomoran untuk menandai pertanyaan, gunakan huruf untuk membuat pilihan jawaban. Gunakan kurung siku dan bintang untuk menandai jawaban yang benar. Contoh:
          </p>

          {isEditing ? (
            <div className="space-y-4">
              {questions.map((question, index) => (
                <div key={question.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pertanyaan {index + 1}:
                    </label>
                    <Textarea
                      value={question.question}
                      onChange={(e) => {
                        const updatedQuestions = questions.map(q =>
                          q.id === question.id ? { ...q, question: e.target.value } : q
                        );
                        setQuestions(updatedQuestions);
                      }}
                      className="w-full"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Jawaban:</label>
                    {question.options.map((option, optionIndex) => (
                      <div key={option.id} className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500 w-6">
                          {String.fromCharCode(97 + optionIndex)}.
                        </span>
                        <Input
                          value={option.text}
                          onChange={(e) => {
                            const updatedQuestions = questions.map(q =>
                              q.id === question.id
                                ? {
                                    ...q,
                                    options: q.options.map(opt =>
                                      opt.id === option.id
                                        ? { ...opt, text: e.target.value }
                                        : opt
                                    )
                                  }
                                : q
                            );
                            setQuestions(updatedQuestions);
                          }}
                          className="flex-1"
                        />
                        <input
                          type="radio"
                          name={`correct-${question.id}`}
                          checked={option.isCorrect}
                          onChange={() => {
                            const updatedQuestions = questions.map(q =>
                              q.id === question.id
                                ? {
                                    ...q,
                                    options: q.options.map(opt => ({
                                      ...opt,
                                      isCorrect: opt.id === option.id
                                    }))
                                  }
                                : q
                            );
                            setQuestions(updatedQuestions);
                          }}
                          className="text-orange-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="border-gray-300 text-gray-600 hover:text-gray-900"
                >
                  Batal
                </Button>
                <Button
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Simpan
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-700 mb-2">
                  1. Mana dari pilihan berikut, yang bukan bagian dari arti petunjuk?
                </p>
                <p className="text-sm text-gray-600 mb-1">Jawaban:</p>
                <p className="text-sm text-gray-600 mb-1">a. Hijau: petunjuk/informasi</p>
                <p className="text-sm text-gray-600 mb-1">b. Kuning: peringatan</p>
                <p className="text-sm text-gray-600 mb-1 font-medium">[*c.] Hitam: Beracun</p>
                <p className="text-sm text-gray-600">d. Merah: larangan</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Right Column - Preview */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Quiz Question Editor</h3>
          <p className="text-sm text-gray-600 mb-6">
            Drag-and-drop untuk mengurutkan pertanyaan.
          </p>

          <div className="space-y-4">
            {questions.map((question, index) => (
              <div key={question.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">
                    Pertanyaan {index + 1}: {question.question}
                  </p>
                  <div className="space-y-2">
                    {question.options.map((option) => (
                      <div key={option.id} className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name={`question-${question.id}`}
                          checked={option.isCorrect}
                          readOnly
                          className="text-orange-500"
                        />
                        <span className={`text-sm ${option.isCorrect ? 'text-gray-900 font-medium' : 'text-gray-600'}`}>
                          {option.text}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900">
          Pengaturan Sistem - Manajemen Konten
        </h1>
      </div>

      <Tabs defaultValue="PAM" className="w-full">
        <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
          <TabsTrigger
            value="PAM"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            PAM
          </TabsTrigger>
          <TabsTrigger
            value="SMA"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            SMA
          </TabsTrigger>
          <TabsTrigger
            value="IBM"
            className="text-sm lg:text-base data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            IBM
          </TabsTrigger>
        </TabsList>

        <TabsContent value="PAM" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <QuizSection
            questions={employeeQuestions}
            isEditing={isEditingEmployee}
            setIsEditing={setIsEditingEmployee}
            setQuestions={setEmployeeQuestions}
          />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <QuizSection
            questions={employeeQuestions}
            isEditing={isEditingEmployee}
            setIsEditing={setIsEditingEmployee}
            setQuestions={setEmployeeQuestions}
          />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <QuizSection
            questions={generalQuestions}
            isEditing={isEditingGeneral}
            setIsEditing={setIsEditingGeneral}
            setQuestions={setGeneralQuestions}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
