import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Edit, Trash2, User } from 'lucide-react';
import { Modal } from '../../components/ui/modal';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';

interface Permission {
  id: string;
  name: string;
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
}

interface Role {
  id: string;
  name: string;
  description: string;
  company?: string;
  permissions: Permission[];
}

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([
    {
      id: '1',
      name: 'Developer',
      description: 'Full akses dashboard, bisa membuat role dan user',
      permissions: [
        { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>gunjun<PERSON>', create: true, read: true, update: true, delete: true },
        { id: '2', name: 'Pengaturan Sistem', create: true, read: true, update: true, delete: true },
        { id: '3', name: 'Backup & Restore', create: true, read: true, update: true, delete: true },
        { id: '4', name: 'Logs', create: true, read: true, update: true, delete: true },
        { id: '5', name: 'Profil', create: true, read: true, update: true, delete: true },
        { id: '6', name: 'Digital Permit', create: true, read: true, update: true, delete: true },
      ]
    },
    {
      id: '2',
      name: 'Super Admin',
      description: 'Full akses dashboard, bisa membuat role dan user',
      permissions: [
        { id: '1', name: 'Manajemen Pengunjung', create: true, read: true, update: true, delete: true },
        { id: '2', name: 'Pengaturan Sistem', create: true, read: true, update: true, delete: true },
        { id: '3', name: 'Backup & Restore', create: true, read: true, update: true, delete: true },
        { id: '4', name: 'Logs', create: true, read: true, update: true, delete: true },
        { id: '5', name: 'Profil', create: true, read: true, update: true, delete: true },
        { id: '6', name: 'Digital Permit', create: true, read: true, update: true, delete: true },
      ]
    },
    {
      id: '3',
      name: 'Admin - PAM',
      description: 'Full akses dashboard perusahaan PAM',
      company: 'PAM',
      permissions: [
        { id: '1', name: 'Manajemen Pengunjung', create: true, read: true, update: true, delete: true },
        { id: '2', name: 'Pengaturan Sistem', create: true, read: true, update: true, delete: true },
        { id: '3', name: 'Backup & Restore', create: true, read: true, update: true, delete: true },
        { id: '4', name: 'Logs', create: true, read: true, update: true, delete: true },
        { id: '5', name: 'Profil', create: true, read: true, update: true, delete: true },
        { id: '6', name: 'Digital Permit', create: true, read: true, update: true, delete: true },
      ]
    },
    {
      id: '4',
      name: 'Admin - SMA',
      description: 'Full akses dashboard perusahaan SMA',
      company: 'SMA',
      permissions: [
        { id: '1', name: 'Manajemen Pengunjung', create: true, read: true, update: true, delete: true },
        { id: '2', name: 'Pengaturan Sistem', create: true, read: true, update: true, delete: true },
        { id: '3', name: 'Backup & Restore', create: true, read: true, update: true, delete: true },
        { id: '4', name: 'Logs', create: true, read: true, update: true, delete: true },
        { id: '5', name: 'Profil', create: true, read: true, update: true, delete: true },
        { id: '6', name: 'Digital Permit', create: true, read: true, update: true, delete: true },
      ]
    },
    {
      id: '5',
      name: 'Admin - IBM',
      description: 'Full akses dashboard perusahaan IBM',
      company: 'IBM',
      permissions: [
        { id: '1', name: 'Manajemen Pengunjung', create: true, read: true, update: true, delete: true },
        { id: '2', name: 'Pengaturan Sistem', create: true, read: true, update: true, delete: true },
        { id: '3', name: 'Backup & Restore', create: true, read: true, update: true, delete: true },
        { id: '4', name: 'Logs', create: true, read: true, update: true, delete: true },
        { id: '5', name: 'Profil', create: true, read: true, update: true, delete: true },
        { id: '6', name: 'Digital Permit', create: true, read: true, update: true, delete: true },
      ]
    }
  ]);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    company: ''
  });

  const defaultPermissions: Permission[] = [
    { id: '1', name: 'Manajemen Pengunjung', create: false, read: false, update: false, delete: false },
    { id: '2', name: 'Pengaturan Sistem', create: false, read: false, update: false, delete: false },
    { id: '3', name: 'Backup & Restore', create: false, read: false, update: false, delete: false },
    { id: '4', name: 'Logs', create: false, read: false, update: false, delete: false },
    { id: '5', name: 'Profil', create: false, read: false, update: false, delete: false },
    { id: '6', name: 'Digital Permit', create: false, read: false, update: false, delete: false },
  ];

  const companyPermissions = {
    PAM: defaultPermissions,
    SMA: defaultPermissions,
    IBM: defaultPermissions
  };

  const [editingPermissions, setEditingPermissions] = useState<Permission[]>(defaultPermissions);

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setFormData({
      name: role.name,
      description: role.description,
      company: role.company || ''
    });
    setEditingPermissions(role.permissions);
    setIsEditModalOpen(true);
  };

  const handleDelete = (roleId: string) => {
    setRoles(roles.filter(role => role.id !== roleId));
  };

  const handleAddRole = () => {
    setFormData({ name: '', description: '', company: '' });
    setEditingPermissions(defaultPermissions);
    setIsAddModalOpen(true);
  };

  const togglePermission = (permissionId: string, type: 'create' | 'read' | 'update' | 'delete') => {
    setEditingPermissions(prev =>
      prev.map(permission =>
        permission.id === permissionId
          ? { ...permission, [type]: !permission[type] }
          : permission
      )
    );
  };

  const saveRole = () => {
    if (editingRole) {
      // Update existing role
      setRoles(roles.map(role => 
        role.id === editingRole.id 
          ? { ...role, ...formData, permissions: editingPermissions }
          : role
      ));
      setIsEditModalOpen(false);
    } else {
      // Add new role
      const newRole: Role = {
        id: Date.now().toString(),
        ...formData,
        permissions: editingPermissions
      };
      setRoles([...roles, newRole]);
      setIsAddModalOpen(false);
    }
    setEditingRole(null);
  };

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900">
          Pengaturan Sistem - Role Management
        </h1>
      </div>

      <Card className="bg-white border border-gray-200">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Edit Role Access</h2>
            <Button 
              onClick={handleAddRole}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 flex items-center gap-2"
            >
              <User className="h-4 w-4" />
              Tambah Role
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Perusahaan</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {roles.map((role) => (
                  <tr key={role.id} className="border-b border-gray-100">
                    <td className="py-4 px-4">
                      <div>
                        <p className="font-medium text-gray-900">{role.name}</p>
                        <p className="text-sm text-gray-600">{role.description}</p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="text-sm text-gray-600">
                        {role.company || '-'}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          onClick={() => handleEdit(role)}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 text-sm flex items-center gap-1"
                        >
                          <Edit className="h-3 w-3" />
                          Edit
                        </Button>
                        <Button
                          onClick={() => handleDelete(role.id)}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 text-sm flex items-center gap-1"
                        >
                          <Trash2 className="h-3 w-3" />
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Role Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Role Access"
      >
        <div className="space-y-6">
          {/* Header with role info and buttons */}
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-1">Role</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="font-medium text-gray-900">{editingRole?.name}</p>
                    <p className="text-sm text-gray-600">{editingRole?.description}</p>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-1">Perusahaan</h4>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-gray-900">{editingRole?.company || '-'}</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 mb-4">
                <Button
                onClick={saveRole}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 flex items-center gap-2"
              >
                <User className="h-4 w-4" />
                Simpan Edit
              </Button>
                <Button
                  onClick={() => handleDelete(editingRole!.id)}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 text-sm flex items-center gap-1"
                >
                  <Trash2 className="h-3 w-3" />
                  Hapus
                </Button>
              </div>
            </div>

          
          </div>

          {/* Permissions Table */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Kapabilitas</h4>

            <div className="overflow-x-auto">
              <table className="w-full border border-gray-200">
                <thead>
                  <tr className="border-b border-gray-200 bg-gray-50">
                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-900">Module</th>
                    <th className="text-center py-3 px-4 text-sm font-medium text-gray-900">Create</th>
                    <th className="text-center py-3 px-4 text-sm font-medium text-gray-900">Read</th>
                    <th className="text-center py-3 px-4 text-sm font-medium text-gray-900">Update</th>
                    <th className="text-center py-3 px-4 text-sm font-medium text-gray-900">Delete</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Developer Section */}
                  <tr className="bg-gray-100">
                    <td colSpan={5} className="py-2 px-4 text-sm font-medium text-gray-700">Developer</td>
                  </tr>
                  {editingPermissions.map((permission) => (
                    <tr key={`dev-${permission.id}`} className="border-b border-gray-100">
                      <td className="py-3 px-4 text-sm text-gray-900">{permission.name}</td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.create}
                          onChange={() => togglePermission(permission.id, 'create')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.read}
                          onChange={() => togglePermission(permission.id, 'read')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.update}
                          onChange={() => togglePermission(permission.id, 'update')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.delete}
                          onChange={() => togglePermission(permission.id, 'delete')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                    </tr>
                  ))}

                  {/* PAM Section */}
                  <tr className="bg-gray-100">
                    <td colSpan={5} className="py-2 px-4 text-sm font-medium text-gray-700">PAM</td>
                  </tr>
                  {companyPermissions.PAM.map((permission) => (
                    <tr key={`pam-${permission.id}`} className="border-b border-gray-100">
                      <td className="py-3 px-4 text-sm text-gray-900">{permission.name}</td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.create}
                          onChange={() => togglePermission(`pam-${permission.id}`, 'create')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.read}
                          onChange={() => togglePermission(`pam-${permission.id}`, 'read')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.update}
                          onChange={() => togglePermission(`pam-${permission.id}`, 'update')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.delete}
                          onChange={() => togglePermission(`pam-${permission.id}`, 'delete')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                    </tr>
                  ))}

                  {/* SMA Section */}
                  <tr className="bg-gray-100">
                    <td colSpan={5} className="py-2 px-4 text-sm font-medium text-gray-700">SMA</td>
                  </tr>
                  {companyPermissions.SMA.map((permission) => (
                    <tr key={`sma-${permission.id}`} className="border-b border-gray-100">
                      <td className="py-3 px-4 text-sm text-gray-900">{permission.name}</td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.create}
                          onChange={() => togglePermission(`sma-${permission.id}`, 'create')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.read}
                          onChange={() => togglePermission(`sma-${permission.id}`, 'read')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.update}
                          onChange={() => togglePermission(`sma-${permission.id}`, 'update')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.delete}
                          onChange={() => togglePermission(`sma-${permission.id}`, 'delete')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                    </tr>
                  ))}

                  {/* IBM Section */}
                  <tr className="bg-gray-100">
                    <td colSpan={5} className="py-2 px-4 text-sm font-medium text-gray-700">IBM</td>
                  </tr>
                  {companyPermissions.IBM.map((permission) => (
                    <tr key={`ibm-${permission.id}`} className="border-b border-gray-100">
                      <td className="py-3 px-4 text-sm text-gray-900">{permission.name}</td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.create}
                          onChange={() => togglePermission(`ibm-${permission.id}`, 'create')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.read}
                          onChange={() => togglePermission(`ibm-${permission.id}`, 'read')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.update}
                          onChange={() => togglePermission(`ibm-${permission.id}`, 'update')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                      <td className="py-3 px-4 text-center">
                        <input
                          type="checkbox"
                          checked={permission.delete}
                          onChange={() => togglePermission(`ibm-${permission.id}`, 'delete')}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </Modal>

      {/* Add Role Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Tambah Role"
      >
        <div className="space-y-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Nama Role *
              </label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Masukkan nama role"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Perusahaan
              </label>
              <Input
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                placeholder="Masukkan nama perusahaan"
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Deskripsi Role
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Masukkan nama perusahaan"
                className="w-full"
                rows={4}
              />
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              onClick={() => setIsAddModalOpen(false)}
              variant="outline"
              className="px-6 py-2"
            >
              Batal
            </Button>
            <Button
              onClick={saveRole}
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2"
            >
              Tambah
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
