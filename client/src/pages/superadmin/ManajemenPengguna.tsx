import { useState, useEffect } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../components/ui/select';
import { Search, Plus, Edit, Trash2 } from 'lucide-react';
import { Modal } from '../../components/ui/modal';
import { Input } from '../../components/ui/input';
import { SimpleSelect } from '../../components/ui/simple-select';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  company: string;
  lastActive: string;
}

export function ManajemenPengguna() {
  const [users, setUsers] = useState<User[]>([]);
  // const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Form states
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: '',
    company: ''
  });

  useEffect(() => {
    // Mock data
    setUsers([
      {
        id: '1',
        name: 'Rina Pratama',
        email: '<EMAIL>',
        role: 'Admin',
        company: 'PAM',
        lastActive: '2024-01-15 10:30'
      },
      {
        id: '2',
        name: 'Budi Santoso',
        email: '<EMAIL>',
        role: 'User',
        company: 'SMA',
        lastActive: '2024-01-14 15:45'
      },
      {
        id: '3',
        name: 'Sari Dewi',
        email: '<EMAIL>',
        role: 'Manager',
        company: 'IBM',
        lastActive: '2024-01-13 09:20'
      },
      {
        id: '4',
        name: 'Ahmad Rahman',
        email: '<EMAIL>',
        role: 'User',
        company: 'PAM',
        lastActive: '2024-01-12 14:10'
      },
      {
        id: '5',
        name: 'Lisa Wijaya',
        email: '<EMAIL>',
        role: 'Admin',
        company: 'SMA',
        lastActive: '2024-01-11 11:25'
      }
    ]);
  }, []);

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.company.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddUser = () => {
    if (formData.name && formData.email && formData.role && formData.company) {
      const newUser: User = {
        id: Date.now().toString(),
        name: formData.name,
        email: formData.email,
        role: formData.role,
        company: formData.company,
        lastActive: new Date().toLocaleString('id-ID')
      };
      setUsers([...users, newUser]);
      setFormData({ name: '', email: '', password: '', role: '', company: '' });
      setShowAddModal(false);
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormData({
      name: user.name,
      email: user.email,
      password: '',
      role: user.role,
      company: user.company
    });
    setShowAddModal(true);
  };

  const handleUpdateUser = () => {
    if (selectedUser && formData.name && formData.email && formData.role && formData.company) {
      setUsers(users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, name: formData.name, email: formData.email, role: formData.role, company: formData.company }
          : user
      ));
      setFormData({ name: '', email: '', password: '', role: '', company: '' });
      setSelectedUser(null);
      setShowAddModal(false);
    }
  };

  const handleDeleteUser = () => {
    if (selectedUser) {
      setUsers(users.filter(user => user.id !== selectedUser.id));
      setSelectedUser(null);
      setShowDeleteModal(false);
    }
  };

  const openDeleteModal = (user: User) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const closeModals = () => {
    setShowAddModal(false);
    setShowDeleteModal(false);
    setSelectedUser(null);
    setFormData({ name: '', email: '', password: '', role: '', company: '' });
  };

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Manajemen Pengguna</h1>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-3">
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>

          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Cari pengguna..."
              className="w-full h-10 pl-10 pr-4 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-orange-500 hover:bg-orange-600 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Tambah Pengguna
        </Button>
      </div>

      {/* Users Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-4 font-medium text-gray-900">Nama</th>
                  <th className="text-left p-4 font-medium text-gray-900">Email</th>
                  <th className="text-left p-4 font-medium text-gray-900">Role</th>
                  <th className="text-left p-4 font-medium text-gray-900">Perusahaan</th>
                  <th className="text-left p-4 font-medium text-gray-900">Terakhir Aktif</th>
                  <th className="text-left p-4 font-medium text-gray-900">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.length > 0 ? (
                  filteredUsers.slice(0, parseInt(entriesPerPage)).map((user) => (
                    <tr key={user.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 font-medium text-gray-900">{user.name}</td>
                      <td className="p-4 text-gray-600">{user.email}</td>
                      <td className="p-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          user.role === 'Admin' ? 'bg-orange-100 text-orange-800' :
                          user.role === 'Manager' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="p-4 text-gray-600">{user.company}</td>
                      <td className="p-4 text-gray-600">{user.lastActive}</td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditUser(user)}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => openDeleteModal(user)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="p-8 text-center text-gray-500">
                      Tidak ada pengguna yang ditemukan
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit User Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={closeModals}
        title={selectedUser ? 'Edit Pengguna' : 'Tambah Pengguna Baru'}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nama Lengkap
            </label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Masukkan nama lengkap"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Masukkan email"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password {selectedUser ? '(Kosongkan jika tidak ingin mengubah)' : ''}
            </label>
            <Input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              placeholder="Masukkan password"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Role
            </label>
            <SimpleSelect
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
            >
              <option value="">Pilih role</option>
              <option value="admin">Admin</option>
              <option value="user">User</option>
            </SimpleSelect>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Perusahaan
            </label>
            <SimpleSelect
              value={formData.company}
              onChange={(e) => setFormData({ ...formData, company: e.target.value })}
            >
              <option value="">Pilih perusahaan</option>
              <option value="PAM">PAM</option>
              <option value="SMA">SMA</option>
              <option value="IBM">IBM</option>
            </SimpleSelect>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={selectedUser ? handleUpdateUser : handleAddUser}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {selectedUser ? 'Update' : 'Tambah'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={closeModals}
        title="Konfirmasi Hapus"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Apakah Anda yakin ingin menghapus pengguna <strong>{selectedUser?.name}</strong>?
            Tindakan ini tidak dapat dibatalkan.
          </p>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={closeModals}>
              Batal
            </Button>
            <Button
              onClick={handleDeleteUser}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Hapus
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
