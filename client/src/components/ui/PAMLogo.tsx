interface PAMLogoProps {
  className?: string;
}

export function PAMLogo({ className = "" }: PAMLogoProps) {
  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* Logo Structure */}
      <div className="relative mb-2">
        {/* Dome/Umbrella Structure */}
        <svg 
          width="120" 
          height="60" 
          viewBox="0 0 120 60" 
          className="text-blue-700"
          fill="currentColor"
        >
          {/* Dome lines */}
          <path d="M10 50 Q60 10 110 50" stroke="currentColor" strokeWidth="2" fill="none" />
          <path d="M15 50 Q60 15 105 50" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <path d="M20 50 Q60 20 100 50" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <path d="M25 50 Q60 25 95 50" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <path d="M30 50 Q60 30 90 50" stroke="currentColor" strokeWidth="1.5" fill="none" />
          <path d="M35 50 Q60 35 85 50" stroke="currentColor" strokeWidth="1.5" fill="none" />
          
          {/* Vertical lines */}
          <line x1="60" y1="10" x2="60" y2="50" stroke="currentColor" strokeWidth="2" />
          <line x1="45" y1="18" x2="45" y2="50" stroke="currentColor" strokeWidth="1.5" />
          <line x1="75" y1="18" x2="75" y2="50" stroke="currentColor" strokeWidth="1.5" />
          <line x1="30" y1="30" x2="30" y2="50" stroke="currentColor" strokeWidth="1.5" />
          <line x1="90" y1="30" x2="90" y2="50" stroke="currentColor" strokeWidth="1.5" />
          
          {/* Base line */}
          <line x1="10" y1="50" x2="110" y2="50" stroke="currentColor" strokeWidth="3" />
        </svg>
      </div>
      
      {/* PAM Text */}
      <div className="bg-yellow-600 px-8 py-2 rounded-sm mb-1">
        <span className="text-white font-bold text-2xl tracking-wider">PAM</span>
      </div>
      
      {/* Company Name */}
      <div className="text-center">
        <div className="text-blue-700 font-bold text-lg">PT. PAM MINERAL Tbk.</div>
      </div>
    </div>
  );
}
