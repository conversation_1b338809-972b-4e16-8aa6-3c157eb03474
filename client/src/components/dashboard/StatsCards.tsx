import { Card, CardContent } from '../ui/card';
import type { DashboardStats } from 'shared/dist';

interface StatsCardsProps {
  stats: DashboardStats;
}

export function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: 'Total Pengunjung Terdaftar',
      value: stats.totalPengunjungTerdaftar,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Disetujui',
      value: stats.totalPengunjungDisetujui,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Ditolak',
      value: stats.totalPengunjungDitolak,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Kadaluwarsa',
      value: stats.totalPengunjungKadaluwarsa,
      bgColor: 'bg-white'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-3 lg:gap-4">
      {cards.map((card, index) => (
        <Card key={index} className={`${card.bgColor} border border-gray-200 shadow-sm`}>
          <CardContent className="p-3 lg:p-4">
            <div className="text-xs lg:text-sm text-gray-600 mb-1 leading-tight">
              {card.title}
            </div>
            <div className="text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900">
              {card.value}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
