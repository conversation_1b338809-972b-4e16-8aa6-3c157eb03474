import { config } from "dotenv";

// Load environment variables first
config({ path: ".env" });

import { betterAuth } from "better-auth"
import { admin } from "better-auth/plugins"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient({
  log: ['error', 'warn'],
})

// Validate secret format
if (!process.env.BETTER_AUTH_SECRET) {
  throw new Error("BETTER_AUTH_SECRET is required but not set");
}

if (process.env.BETTER_AUTH_SECRET.length < 32) {
  throw new Error("BETTER_AUTH_SECRET should be at least 32 characters long");
}

// Prepare admin user IDs safely
const adminUserIds: string[] = [];
if (process.env.SUPERADMIN_ID) {
  const superadminId = process.env.SUPERADMIN_ID.trim();
  if (superadminId.length > 0) {
    adminUserIds.push(superadminId);
  }
}

export const auth: any = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
  trustedOrigins: ["http://localhost:5173", "http://localhost:3000"],
  plugins: [
    admin(),
  ],
})

export type Session = typeof auth.$Infer.Session
