import { config } from "dotenv";

// Load environment variables first
config({ path: ".env" });

import { betterAuth } from "better-auth"
// import { admin } from "better-auth/plugins"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient({
  log: ['error', 'warn'],
})

// Debug environment variables
console.log("🔍 Auth module - Environment variables check:");
console.log(`   BETTER_AUTH_SECRET: ${process.env.BETTER_AUTH_SECRET ? 'LOADED ✅' : 'MISSING ❌'}`);
console.log(`   BETTER_AUTH_SECRET length: ${process.env.BETTER_AUTH_SECRET?.length || 0}`);
console.log(`   BETTER_AUTH_URL: ${process.env.BETTER_AUTH_URL || 'Using default'}`);
console.log(`   SUPERADMIN_ID: ${process.env.SUPERADMIN_ID || 'Not set'}`);

// Validate secret format
if (!process.env.BETTER_AUTH_SECRET) {
  throw new Error("BETTER_AUTH_SECRET is required but not set");
}

if (process.env.BETTER_AUTH_SECRET.length < 32) {
  console.warn("⚠️  BETTER_AUTH_SECRET should be at least 32 characters long");
}

// Prepare admin user IDs safely
const adminUserIds: string[] = [];
if (process.env.SUPERADMIN_ID) {
  const superadminId = process.env.SUPERADMIN_ID.trim();
  if (superadminId.length > 0) {
    adminUserIds.push(superadminId);
    console.log(`✅ Added superadmin ID: ${superadminId}`);
  }
}

export const auth: any = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
  trustedOrigins: ["http://localhost:5173", "http://localhost:3000"],
})

export type Session = typeof auth.$Infer.Session
