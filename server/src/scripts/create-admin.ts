import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function createAdmin() {
  console.log("👤 Create New Admin/Superadmin");
  console.log("==============================");

  // Konfigurasi admin baru
  const ADMIN_EMAIL = "<EMAIL>"; // Email superadmin
  const ADMIN_PASSWORD = "admin123"; // Password superadmin
  const ADMIN_NAME = "Super Administrator"; // Nama superadmin
  const ADMIN_ROLE = "superadmin"; // Role superadmin

  try {
    // 1. Cek apakah email sudah ada
    const existingUser = await prisma.user.findUnique({
      where: { email: ADMIN_EMAIL }
    });

    if (existingUser) {
      console.log("❌ Email sudah terdaftar:", ADMIN_EMAIL);
      return;
    }

    // 2. Buat user baru
    const newUser = await prisma.user.create({
      data: {
        email: ADMIN_EMAIL,
        name: <PERSON><PERSON><PERSON>_NAME,
        role: ADMIN_ROLE,
        emailVerified: true, // Set sebagai verified
      }
    });

    console.log("✅ User berhasil dibuat!");
    console.log("📧 Email:", newUser.email);
    console.log("🆔 User ID:", newUser.id);
    console.log("👤 Role:", newUser.role);

    // 3. Hash password
    const hashedPassword = await bcrypt.hash(ADMIN_PASSWORD, 12);

    // 4. Buat account credential
    await prisma.account.create({
      data: {
        accountId: newUser.email,
        providerId: "credential",
        userId: newUser.id,
        password: hashedPassword,
      }
    });

    console.log("✅ Account credential berhasil dibuat!");

    console.log("");
    console.log("🎉 Admin baru berhasil dibuat!");
    console.log("📝 Login credentials:");
    console.log(`   Email: ${ADMIN_EMAIL}`);
    console.log(`   Password: ${ADMIN_PASSWORD}`);
    console.log(`   Role: ${ADMIN_ROLE}`);
    console.log("");

    if (ADMIN_ROLE === "superadmin") {
      console.log("⚠️  Untuk superadmin, tambahkan ID ini ke .env:");
      console.log(`SUPERADMIN_ID="${newUser.id}"`);
      console.log("   Atau tambahkan ke array jika sudah ada superadmin lain");
    }

  } catch (err) {
    console.error("❌ Error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createAdmin()
  .then(() => {
    console.log("🏁 Script selesai");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Script gagal:", err);
    process.exit(1);
  });
