import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function updateSuperadmin() {
  console.log("🔧 Update Superadmin Credentials");
  console.log("================================");

  // Konfigurasi baru
  const NEW_EMAIL = "<EMAIL>"; // Ganti dengan email baru
  const NEW_PASSWORD = "********"; // Ganti dengan password baru
  const NEW_NAME = "PAM Administrator"; // Ganti dengan nama baru

  try {
    // 1. Cari superadmin yang ada
    const existingSuperadmin = await prisma.user.findFirst({
      where: { role: "superadmin" },
      include: { accounts: true }
    });

    if (!existingSuperadmin) {
      console.log("❌ Tidak ada superadmin yang ditemukan!");
      return;
    }

    console.log("📧 Email lama:", existingSuperadmin.email);
    console.log("🆔 User ID:", existingSuperadmin.id);

    // 2. Update email dan nama di tabel User
    await prisma.user.update({
      where: { id: existingSuperadmin.id },
      data: {
        email: NEW_EMAIL,
        name: NEW_NAME,
      }
    });

    console.log("✅ Email dan nama berhasil diupdate!");

    // 3. Hash password baru
    const hashedPassword = await bcrypt.hash(NEW_PASSWORD, 12);

    // 4. Update atau create account dengan password baru
    const existingAccount = existingSuperadmin.accounts.find(
      acc => acc.providerId === "credential"
    );

    if (existingAccount) {
      // Update existing account
      await prisma.account.update({
        where: { id: existingAccount.id },
        data: { password: hashedPassword }
      });
      console.log("✅ Password berhasil diupdate!");
    } else {
      // Create new account
      await prisma.account.create({
        data: {
          accountId: existingSuperadmin.email,
          providerId: "credential",
          userId: existingSuperadmin.id,
          password: hashedPassword,
        }
      });
      console.log("✅ Account credential berhasil dibuat!");
    }

    console.log("");
    console.log("🎉 Superadmin credentials berhasil diupdate!");
    console.log("📝 Credentials baru:");
    console.log(`   Email: ${NEW_EMAIL}`);
    console.log(`   Password: ${NEW_PASSWORD}`);
    console.log(`   Nama: ${NEW_NAME}`);
    console.log("");
    console.log("⚠️  Jangan lupa update SUPERADMIN_ID di .env jika diperlukan:");
    console.log(`SUPERADMIN_ID="${existingSuperadmin.id}"`);

  } catch (err) {
    console.error("❌ Error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
updateSuperadmin()
  .then(() => {
    console.log("🏁 Script selesai");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Script gagal:", err);
    process.exit(1);
  });
