import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seedSuperadmin() {
  console.log("🌱 Creating superadmin account...");

  try {
    // Delete existing superadmin if exists
    await prisma.user.deleteMany({
      where: { email: "<EMAIL>" }
    });

    console.log("�️  Deleted existing superadmin (if any)");

    // Create superadmin user with plain password (Better Auth will handle hashing)
    const superadmin = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        password: "admin123", // Let Better Auth handle password creation
        name: "Super Administrator",
        role: "superadmin",
        emailVerified: true,
      },
    });

    console.log("✅ Superadmin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🆔 User ID:", superadmin.id);
    console.log("");
    console.log("⚠️  IMPORTANT: Add this ID to your .env file:");
    console.log(`SUPERADMIN_ID="${superadmin.id}"`);
    console.log("");
    console.log("� Now you need to set password via Better Auth signup API");
    console.log("   Use the signup endpoint with this email to set the password");

  } catch (err) {
    console.error("❌ Unexpected error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
seedSuperadmin()
  .then(() => {
    console.log("🏁 Seeder completed");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Seeder failed:", err);
    process.exit(1);
  });
