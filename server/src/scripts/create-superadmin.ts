import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function createSuperadmin() {
  console.log("🌱 Creating superadmin account...");

  try {
    // Use Better Auth signup API via HTTP request
    const response = await fetch("http://localhost:3000/api/auth/sign-up/email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "********",
        name: "Super Administrator",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Signup failed:", response.status, errorText);
      return;
    }

    const data = await response.json() as any;
    console.log("✅ User created via Better Auth signup!");

    if (data.user) {
      const user = data.user;
      console.log("📧 Email:", user.email);
      console.log("🆔 User ID:", user.id);
      console.log("🔑 Password: SuperAdmin123!");

      // Update role directly in database
      await prisma.user.update({
        where: { id: user.id },
        data: { role: "superadmin" }
      });

      console.log("✅ Role updated to superadmin!");
      console.log("");
      console.log("⚠️  IMPORTANT: Add this ID to your .env file:");
      console.log(`SUPERADMIN_ID="${user.id}"`);
      console.log("");
      console.log("🎉 Superadmin setup complete!");
      console.log("📝 You can now login with:");
      console.log("   Email: <EMAIL>");
      console.log("   Password: SuperAdmin123!");
    }

  } catch (err) {
    console.error("❌ Unexpected error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the creator
createSuperadmin()
  .then(() => {
    console.log("🏁 Creator completed");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Creator failed:", err);
    process.exit(1);
  });
