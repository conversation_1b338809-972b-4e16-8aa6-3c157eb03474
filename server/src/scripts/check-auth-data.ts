import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function checkAuthData() {
  console.log("🔍 Checking Auth Data in Database");
  console.log("=================================");

  try {
    // 1. Get all users
    const users = await prisma.user.findMany({
      include: {
        accounts: true,
        sessions: true,
      },
      orderBy: { createdAt: 'asc' }
    });

    console.log(`📊 Total users: ${users.length}`);
    console.log("");

    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Name: ${user.name || 'N/A'}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Password in User table: ${user.password || 'NULL (NORMAL)'}`);
      console.log(`   Email Verified: ${user.emailVerified}`);
      console.log(`   Created: ${user.createdAt.toISOString()}`);
      
      // Check accounts
      console.log(`   📋 Accounts (${user.accounts.length}):`);
      user.accounts.forEach((account, accIndex) => {
        console.log(`      Account ${accIndex + 1}:`);
        console.log(`         Provider: ${account.providerId}`);
        console.log(`         Account ID: ${account.accountId}`);
        console.log(`         Has Password Hash: ${account.password ? 'YES ✅' : 'NO ❌'}`);
        if (account.password) {
          console.log(`         Password Hash: ${account.password.substring(0, 20)}...`);
        }
      });

      // Check sessions
      console.log(`   🔐 Active Sessions (${user.sessions.length}):`);
      user.sessions.forEach((session, sessIndex) => {
        const isExpired = session.expiresAt < new Date();
        console.log(`      Session ${sessIndex + 1}: ${isExpired ? 'EXPIRED ❌' : 'ACTIVE ✅'}`);
        console.log(`         Expires: ${session.expiresAt.toISOString()}`);
      });

      console.log("");
    });

    // 2. Check for orphaned accounts
    const orphanedAccounts = await prisma.account.findMany({
      where: {
        userId: {
          notIn: users.map(u => u.id)
        }
      }
    });

    if (orphanedAccounts.length > 0) {
      console.log(`⚠️  Found ${orphanedAccounts.length} orphaned accounts!`);
      orphanedAccounts.forEach((account, index) => {
        console.log(`   Orphaned Account ${index + 1}: ${account.accountId} (${account.providerId})`);
      });
    }

    // 3. Summary
    console.log("📈 Summary:");
    console.log(`   Total Users: ${users.length}`);
    console.log(`   Users with Credentials: ${users.filter(u => u.accounts.some(a => a.providerId === 'credential')).length}`);
    console.log(`   Superadmins: ${users.filter(u => u.role === 'superadmin').length}`);
    console.log(`   Admins: ${users.filter(u => u.role === 'admin').length}`);
    console.log(`   Regular Users: ${users.filter(u => u.role === 'user').length}`);

  } catch (err) {
    console.error("❌ Error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
checkAuthData()
  .then(() => {
    console.log("🏁 Check completed");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Check failed:", err);
    process.exit(1);
  });
