import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function clearSessions() {
  console.log("🧹 Clearing all sessions...");
  
  try {
    const result = await prisma.session.deleteMany({});
    console.log(`✅ Deleted ${result.count} sessions`);
    console.log("🔄 This will force all users to log in again");
  } catch (err) {
    console.error("❌ Error:", err);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
clearSessions()
  .then(() => {
    console.log("🏁 Session cleanup completed");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Session cleanup failed:", err);
    process.exit(1);
  });
