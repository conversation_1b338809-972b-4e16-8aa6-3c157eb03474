{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch", "seed:superadmin": "bun run src/scripts/seed-superadmin.ts", "create:superadmin": "bun run src/scripts/create-superadmin.ts", "update:superadmin": "bun run src/scripts/update-superadmin.ts", "create:admin": "bun run src/scripts/create-admin.ts", "check:auth": "bun run src/scripts/check-auth-data.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "better-auth": "1.2.12", "dotenv": "^17.2.0", "hono": "^4.7.11", "prisma": "^6.12.0", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest"}}